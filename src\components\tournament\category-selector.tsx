'use client';

import * as React from 'react';
import { Combobox, type ComboboxOption } from '@/components/common/combobox';
import { Tag, Plus } from 'lucide-react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@convex/_generated/api';
import { CategoryCreateDialog } from './category-create-dialog';

// Predefined tournament categories
const PREDEFINED_CATEGORIES: ComboboxOption[] = [
  { value: 'open', label: 'Open' },
  { value: 'women', label: 'Women' },
  { value: 'under-8', label: 'Under 8' },
  { value: 'under-10', label: 'Under 10' },
  { value: 'under-12', label: 'Under 12' },
  { value: 'under-14', label: 'Under 14' },
  { value: 'under-16', label: 'Under 16' },
  { value: 'under-18', label: 'Under 18' },
  { value: 'under-20', label: 'Under 20' },
  { value: 'senior', label: 'Senior (50+)' },
  { value: 'veteran', label: '<PERSON>eteran (65+)' },
  { value: 'beginner', label: 'Beginner' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'advanced', label: 'Advanced' },
];

export interface CategorySelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  label: string;
  disabled?: boolean;
  error?: string;
  className?: string;
  containerClassName?: string;
}

export function CategorySelector({
  value,
  onValueChange,
  label,
  disabled = false,
  error,
  className,
  containerClassName,
}: CategorySelectorProps) {
  const [customCategories, setCustomCategories] = React.useState<
    ComboboxOption[]
  >([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = React.useState(false);

  // Fetch categories from database
  const dbCategoriesQuery = useQuery(api.categories.getCategories);
  const dbCategories = React.useMemo(
    () => dbCategoriesQuery || [],
    [dbCategoriesQuery],
  );
  const seedDefaultCategories = useMutation(
    api.categories.seedDefaultCategories,
  );

  // Auto-seed default categories if none exist
  React.useEffect(() => {
    if (dbCategories.length === 0) {
      seedDefaultCategories().catch((error) => {
        console.error('Failed to seed default categories:', error);
      });
    }
  }, [dbCategories.length, seedDefaultCategories]);

  // Convert database categories to ComboboxOption format
  const dbCategoryOptions: ComboboxOption[] = React.useMemo(() => {
    return dbCategories.map((cat) => ({
      value: cat.name.toLowerCase().replace(/\s+/g, '-'),
      label: cat.name,
    }));
  }, [dbCategories]);

  // Combine predefined, database, and custom categories
  const allCategories = React.useMemo(() => {
    return [
      ...PREDEFINED_CATEGORIES,
      ...dbCategoryOptions,
      ...customCategories,
    ];
  }, [dbCategoryOptions, customCategories]);

  // Handle value change - if it's a new value, add it as a custom category
  const handleValueChange = (newValue: string) => {
    // If the value doesn't exist in our options, add it as a custom category
    const existingOption = allCategories.find(
      (option) =>
        option.value === newValue ||
        option.label.toLowerCase() === newValue.toLowerCase(),
    );

    if (!existingOption && newValue.trim()) {
      const customOption: ComboboxOption = {
        value: newValue.toLowerCase().replace(/\s+/g, '-'),
        label: newValue,
      };
      setCustomCategories((prev) => [...prev, customOption]);
      onValueChange(newValue); // Use the original value as entered by user
    } else {
      // Use the existing option's label if found, otherwise use the value as-is
      const finalValue = existingOption ? existingOption.label : newValue;
      onValueChange(finalValue);
    }
  };

  // Find the current option for display
  const currentOption = allCategories.find(
    (option) => option.value === value || option.label === value,
  );

  const displayValue = currentOption ? currentOption.value : value;

  // Handle successful category creation
  const handleCategoryCreated = (categoryName: string) => {
    setIsCreateDialogOpen(false);
    onValueChange(categoryName);
  };

  return (
    <div className={containerClassName}>
      <Combobox
        value={displayValue}
        onValueChange={handleValueChange}
        options={allCategories}
        label={label}
        placeholder="Select or create a category..."
        searchPlaceholder="Search or type to create new category..."
        emptyMessage="No categories found."
        disabled={disabled}
        error={error}
        className={className}
        searchable={true}
        allowCustom={true}
        customOptionLabel="Create category"
        icon={<Tag className="h-4 w-4" />}
        iconPosition="right"
      />

      {/* Create New Category Link */}
      <div className="mt-2 text-center">
        <span
          onClick={() => !disabled && setIsCreateDialogOpen(true)}
          className={`text-sm transition-colors inline-flex items-center gap-1 cursor-pointer ${
            disabled
              ? 'text-muted-foreground/50 cursor-not-allowed'
              : 'text-muted-foreground hover:text-primary'
          }`}
          role="button"
          tabIndex={disabled ? -1 : 0}
          onKeyDown={(e) => {
            if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
              e.preventDefault();
              setIsCreateDialogOpen(true);
            }
          }}
        >
          <Plus className="h-3 w-3" />
          Did not find the category? Create New Category
        </span>
      </div>

      {/* Category Creation Dialog */}
      <CategoryCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCategoryCreated={handleCategoryCreated}
      />
    </div>
  );
}
